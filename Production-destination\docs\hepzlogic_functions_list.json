[{"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggere5bf864ba9", "route": "analytics/advanced", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "advanced-analytics-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/advanced-analytics-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/advanced-analytics-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics/advanced", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/advanced-analytics-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/advanced-analytics-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere76bf4f677", "route": "permissions/check-advanced", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "advanced-permission-check", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/advanced-permission-check", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/advanced-permission-check", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/permissions/check-advanced", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/advanced-permission-check", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/advanced-permission-check.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger1826e001c2", "route": "management/advanced-roles", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "advanced-role-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/advanced-role-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/advanced-role-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/advanced-roles", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/advanced-role-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/advanced-role-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger242361e5ac", "route": "documents/{id}/ai-analysis", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-document-analysis", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-document-analysis", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-document-analysis", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/ai-analysis", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-document-analysis", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-document-analysis.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger6d2e9ff966", "route": "ai/forms/process", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-form-process", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-form-process", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-form-process", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/forms/process", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-form-process", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-form-process.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerb057b0a407", "route": "search/intelligent", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-intelligent-search", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-intelligent-search", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-intelligent-search", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/search/intelligent", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-intelligent-search", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-intelligent-search.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggercdf871eec9", "route": "ai/models", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-model-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-model-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-model-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/models", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-model-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-model-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger892b507d33", "route": "ai/models/{modelId}/deploy", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-model-deploy", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-model-deploy", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-model-deploy", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/models/{modelid}/deploy", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-model-deploy", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-model-deploy.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger1e702eeced", "route": "ai/models/{modelId}/train", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-model-train", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-model-train", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-model-train", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/models/{modelid}/train", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-model-train", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-model-train.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerc137fd49ca", "route": "ai/operations", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-operation-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-operation-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-operation-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/operations", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-operation-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-operation-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerfc15eeef29", "route": "ai/operations/{operationId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "ai-operation-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/ai-operation-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/ai-operation-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/operations/{operationid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/ai-operation-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/ai-operation-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger1b3671636b", "route": "analytics/dashboard", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "analytics-dashboard", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/analytics-dashboard", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/analytics-dashboard", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics/dashboard", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/analytics-dashboard", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/analytics-dashboard.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggeraadfc25faa", "route": "analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "analytics-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/analytics-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/analytics-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/analytics-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/analytics-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "SERVICE_BUS_CONNECTION_STRING", "direction": "in", "name": "serviceBusTriggerb9c7846e95", "subscriptionName": "analytics-aggregator", "topicName": "analytics-events", "type": "serviceBusTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "analyticsAggregation", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/analyticsAggregation", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/analyticsAggregation", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/analyticsAggregation", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/analyticsAggregation.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger59c1dee283", "route": "integrations/api-connections", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-connection-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-connection-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-connection-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/integrations/api-connections", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-connection-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-connection-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerb7e235019d", "route": "integrations/api-connections/test", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-connection-test", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-connection-test", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-connection-test", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/integrations/api-connections/test", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-connection-test", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-connection-test.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggera97cf00ce3", "route": "api-keys/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-key-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-key-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-key-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/api-keys/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-key-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-key-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["DELETE", "OPTIONS"], "name": "httpTrigger164a693738", "route": "api-keys/{apiKeyId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-key-revoke", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-key-revoke", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-key-revoke", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/api-keys/{apikeyid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-key-revoke", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-key-revoke.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger865d22266d", "route": "api-keys/validate", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-key-validate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-key-validate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-key-validate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/api-keys/validate", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-key-validate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-key-validate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerf058ae5b36", "route": "api-keys", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "api-keys", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/api-keys", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/api-keys", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/api-keys", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/api-keys", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/api-keys.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger772b93f528", "route": "audit/logs/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "audit-log-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/audit-log-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/audit-log-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/audit/logs/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/audit-log-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/audit-log-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7a191bb68e", "route": "audit/logs/export", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "audit-logs-export", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/audit-logs-export", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/audit-logs-export", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/audit/logs/export", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/audit-logs-export", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/audit-logs-export.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger2b130a1b8a", "route": "audit/logs/get", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "audit-logs-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/audit-logs-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/audit-logs-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/audit/logs/get", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/audit-logs-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/audit-logs-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger762991ac0f", "route": "audit/logs", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "audit-logs-list", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/audit-logs-list", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/audit-logs-list", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/audit/logs", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/audit-logs-list", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/audit-logs-list.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger3b272d1a86", "route": "audit/statistics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "audit-statistics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/audit-statistics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/audit-statistics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/audit/statistics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/audit-statistics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/audit-statistics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerb9152bd78a", "route": "auth/login", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "auth-login", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/auth-login", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/auth-login", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/auth/login", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/auth-login", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/auth-login.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerbd1848525f", "route": "auth/logout", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "auth-logout", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/auth-logout", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/auth-logout", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/auth/logout", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/auth-logout", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/auth-logout.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger3178136c92", "route": "auth/me", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "auth-me", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/auth-me", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/auth-me", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/auth/me", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/auth-me", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/auth-me.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger0c814dcf78", "route": "auth/refresh", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "auth-refresh", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/auth-refresh", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/auth-refresh", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/auth/refresh", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/auth-refresh", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/auth-refresh.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerf64b3d0b51", "route": "auth/register", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "auth-register", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/auth-register", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/auth-register", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/auth/register", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/auth-register", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/auth-register.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere30e722a79", "route": "automations", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "automation-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/automation-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/automation-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/automations", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/automation-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/automation-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8e275dc3d7", "route": "automations/execute", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "automation-execute", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/automation-execute", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/automation-execute", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/automations/execute", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/automation-execute", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/automation-execute.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger15e682b266", "route": "management/backups/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "backup-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/backup-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/backup-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/backups/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/backup-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/backup-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger8a3822d20e", "route": "management/backups/{backupId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "backup-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/backup-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/backup-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/backups/{backupid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/backup-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/backup-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerafd75dc253", "route": "ai/batch-jobs", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "batch-job-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/batch-job-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/batch-job-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/batch-jobs", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/batch-job-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/batch-job-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger18a7933dfa", "route": "ai/batch-jobs/{batchJobId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "batch-job-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/batch-job-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/batch-job-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/ai/batch-jobs/{batchjobid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/batch-job-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/batch-job-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger057fab9ea4", "route": "analytics/bi/dashboard", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "bi-dashboard-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/bi-dashboard-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/bi-dashboard-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics/bi/dashboard", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/bi-dashboard-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/bi-dashboard-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerfc094a528e", "route": "analytics/bi/reports", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "bi-report-generate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/bi-report-generate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/bi-report-generate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics/bi/reports", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/bi-report-generate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/bi-report-generate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["DELETE", "OPTIONS"], "name": "httpTrigger5a66cf4832", "route": "management/cache/clear", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "cache-clear", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/cache-clear", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/cache-clear", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/cache/clear", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/cache-clear", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/cache-clear.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger5b78558700", "route": "management/cache/operations", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "cache-operation", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/cache-operation", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/cache-operation", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/cache/operations", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/cache-operation", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/cache-operation.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerb6a2611449", "route": "management/cache/statistics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "cache-statistics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/cache-statistics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/cache-statistics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/cache/statistics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/cache-statistics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/cache-statistics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerfcda9b4087", "route": "channels", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "channel-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/channel-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/channel-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/channels", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/channel-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/channel-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger9cd1b74d85", "route": "classification/categories", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "classification-category-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/classification-category-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/classification-category-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/classification/categories", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/classification-category-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/classification-category-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger219b4e3893", "route": "collaboration/sessions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "collaboration-session-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/collaboration-session-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/collaboration-session-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/collaboration/sessions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/collaboration-session-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/collaboration-session-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger49867912c6", "route": "collaboration/sessions/join", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "collaboration-session-join", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/collaboration-session-join", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/collaboration-session-join", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/collaboration/sessions/join", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/collaboration-session-join", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/collaboration-session-join.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggeradec042002", "route": "comments", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "comment-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/comment-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/comment-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/comments", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/comment-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/comment-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger0a796fdd26", "route": "comments/reactions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "comment-reaction-add", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/comment-reaction-add", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/comment-reaction-add", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/comments/reactions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/comment-reaction-add", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/comment-reaction-add.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8a9ddc3032", "route": "compliance/assessments", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "compliance-assessment-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/compliance-assessment-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/compliance-assessment-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/compliance/assessments", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/compliance-assessment-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/compliance-assessment-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger97866aa75a", "route": "compliance/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "compliance-status-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/compliance-status-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/compliance-status-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/compliance/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/compliance-status-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/compliance-status-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger296bdeccc9", "route": "config", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "config-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/config-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/config-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/config", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/config-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/config-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "POST", "OPTIONS"], "name": "httpTrigger6de0ca6b7a", "route": "management/configuration", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "config-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/config-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/config-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/configuration", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/config-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/config-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"direction": "in", "name": "eventGridTrigger69f302e322", "type": "eventGridTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "custom-events-trigger", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/custom-events-trigger", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/custom-events-trigger", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/custom-events-trigger", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/custom-events-trigger.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggeracb50c5e13", "route": "reports/custom", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "custom-report-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/custom-report-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/custom-report-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/reports/custom", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/custom-report-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/custom-report-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger73ec3e49e2", "route": "reports/custom/{reportId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "custom-report-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/custom-report-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/custom-report-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/reports/custom/{reportid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/custom-report-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/custom-report-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"direction": "in", "name": "timerTrigger5e4dee41fc", "schedule": "0 0 2 * * *", "type": "timer<PERSON><PERSON>ger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "dailyCleanupTimer", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/dailyCleanupTimer", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/dailyCleanupTimer", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/dailyCleanupTimer", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/dailyCleanupTimer.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger73d9325538", "route": "dashboards", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "dashboard-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/dashboard-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/dashboard-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/dashboards", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/dashboard-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/dashboard-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger38ecb1ff5a", "route": "dashboards/{dashboardId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "dashboard-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/dashboard-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/dashboard-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/dashboards/{dashboardid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/dashboard-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/dashboard-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger422a98dc8f", "route": "security/decrypt", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "data-decrypt", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/data-decrypt", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/data-decrypt", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/security/decrypt", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/data-decrypt", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/data-decrypt.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerf4a754715f", "route": "security/encrypt", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "data-encrypt", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/data-encrypt", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/data-encrypt", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/security/encrypt", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/data-encrypt", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/data-encrypt.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerf4380c74d7", "route": "exports", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "data-export-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/data-export-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/data-export-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/exports", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/data-export-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/data-export-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger06a80b238d", "route": "exports/{exportId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "data-export-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/data-export-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/data-export-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/exports/{exportid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/data-export-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/data-export-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "queueTrigger31c19f361b", "queueName": "dead-letter-queue", "type": "queueTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "deadLetter<PERSON><PERSON><PERSON>", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/deadLetterQueue", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/deadLetterQueue", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/deadLetterQ<PERSON>ue", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/deadLetterQueue.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7375cdf948", "route": "notifications/devices", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "device-register", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/device-register", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/device-register", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/devices", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/device-register", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/device-register.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger9d7b6724a4", "route": "documents/approvals", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-approval-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-approval-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-approval-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/approvals", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-approval-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-approval-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger99b6ab81ae", "route": "documents/{documentId}/archive", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-archive", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-archive", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-archive", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/archive", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-archive", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-archive.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggera4217c7d92", "route": "documents/{documentId}/classify", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-classify", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-classify", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-classify", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/classify", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-classify", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-classify.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggere5149c2a89", "route": "documents/{documentId}/comments", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-comments", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-comments", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-comments", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/comments", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-comments", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-comments.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger65c51e1a49", "route": "documents/{id}/complete-content", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-complete-content", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-complete-content", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-complete-content", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/complete-content", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-complete-content", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-complete-content.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger3e6efc728c", "route": "documents/{id}/enhance", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-enhance", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-enhance", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-enhance", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/enhance", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-enhance", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-enhance.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger0a337e3cca", "route": "documents/{documentId}/intelligence/comprehensive", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-intelligence-comprehensive", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-intelligence-comprehensive", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-intelligence-comprehensive", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/intelligence/comprehensive", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-intelligence-comprehensive", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-intelligence-comprehensive.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger0b1babc3da", "route": "documents", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-list", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-list", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-list", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-list", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-list.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerfdb130b602", "route": "documents/metadata/extract", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-metadata-extract", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-metadata-extract", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-metadata-extract", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/metadata/extract", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-metadata-extract", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-metadata-extract.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger3bbb4e9d5f", "route": "documents/{documentId}/metadata", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-metadata-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-metadata-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-metadata-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/metadata", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-metadata-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-metadata-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger91d13367c6", "route": "documents/process", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-processing", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-processing", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-processing", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/process", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-processing", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-processing.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerc527b15456", "route": "documents/{documentId}/restore", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-restore", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-restore", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-restore", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/restore", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-restore", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-restore.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger27a7f350b0", "route": "documents/{id}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-retrieve", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-retrieve", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-retrieve", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-retrieve", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-retrieve.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger363b007b79", "route": "documents/approvals/review", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-review", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-review", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-review", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/approvals/review", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-review", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-review.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8d5682a964", "route": "documents/{id}/share", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-share", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-share", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-share", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/share", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-share", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-share.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerdd640a7768", "route": "documents/{documentId}/share", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-share-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-share-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-share-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/share", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-share-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-share-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerc28791d8bb", "route": "documents/{documentId}/shares", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-share-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-share-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-share-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/shares", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-share-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-share-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger61dd78ebfa", "route": "documents/sign", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-sign", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-sign", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-sign", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/sign", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-sign", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-sign.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggeraab1dc9581", "route": "documents/{id}/specialized-processing", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-specialized-processing", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-specialized-processing", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-specialized-processing", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/specialized-processing", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-specialized-processing", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-specialized-processing.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger96a5083763", "route": "document-templates/generate", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-template-generate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-template-generate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-template-generate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/document-templates/generate", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-template-generate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-template-generate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7f4c89318a", "route": "documents/transform", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-transform", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-transform", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-transform", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/transform", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-transform", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-transform.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerae61e297a7", "route": "documents/upload", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-upload", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-upload", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-upload", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/upload", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-upload", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-upload.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerac0a84f19b", "route": "documents/{documentId}/upload/complete", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-upload-complete", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-upload-complete", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-upload-complete", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/upload/complete", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-upload-complete", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-upload-complete.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggeraa94321456", "route": "documents/{documentId}/versions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-version-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-version-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-version-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/versions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-version-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-version-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere1d79d539f", "route": "documents/{documentId}/versions/{versionId}/restore", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-version-restore", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-version-restore", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-version-restore", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{documentid}/versions/{versionid}/restore", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-version-restore", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-version-restore.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger81e79a409a", "route": "documents/{id}/versions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "document-versions", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/document-versions", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/document-versions", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/documents/{id}/versions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/document-versions", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/document-versions.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "blobTrigger1880ed41fd", "path": "documents/{name}", "type": "blobTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "documentBlobTrigger", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/documentBlobTrigger", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/documentBlobTrigger", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/documentBlobTrigger", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/documentBlobTrigger.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "SERVICE_BUS_CONNECTION_STRING", "direction": "in", "name": "serviceBusTrigger2b06e40ae8", "subscriptionName": "collaboration-processor", "topicName": "document-collaboration", "type": "serviceBusTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "documentCollaboration", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/documentCollaboration", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/documentCollaboration", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/documentCollaboration", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/documentCollaboration.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "queueTrigger3d6b76b5d0", "queueName": "document-processing", "type": "queueTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "documentProcessingQueue", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/documentProcessingQueue", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/documentProcessingQueue", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/documentProcessingQueue", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/documentProcessingQueue.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger390e1be062", "route": "emails/automation/send", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "email-automation-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/email-automation-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/email-automation-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/emails/automation/send", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/email-automation-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/email-automation-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger75c1650914", "route": "emails/automation/templates", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "email-automation-template-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/email-automation-template-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/email-automation-template-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/emails/automation/templates", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/email-automation-template-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/email-automation-template-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger3ceb9824fa", "route": "emails", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "email-list", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/email-list", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/email-list", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/emails", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/email-list", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/email-list.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger3fb00d8d7b", "route": "emails/send", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "email-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/email-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/email-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/emails/send", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/email-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/email-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger3bb238be7c", "route": "emails/templates", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "email-template-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/email-template-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/email-template-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/emails/templates", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/email-template-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/email-template-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "queueTrigger10c571c95a", "queueName": "emails", "type": "queueTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "emailQueue", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/emailQueue", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/emailQueue", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/emailQueue", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/emailQueue.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger4bd2cdf81a", "route": "integrations/enterprise", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "enterprise-integration-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/enterprise-integration-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/enterprise-integration-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/integrations/enterprise", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/enterprise-integration-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/enterprise-integration-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger4ba0902b2c", "route": "integrations/enterprise/sync", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "enterprise-integration-sync", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/enterprise-integration-sync", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/enterprise-integration-sync", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/integrations/enterprise/sync", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/enterprise-integration-sync", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/enterprise-integration-sync.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerf87c345644", "route": "eventgrid/publish", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "event-grid-publish", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/event-grid-publish", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/event-grid-publish", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/eventgrid/publish", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/event-grid-publish", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/event-grid-publish.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger63f9e36082", "route": "eventgrid/webhook", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "event-grid-webhook", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/event-grid-webhook", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/event-grid-webhook", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/eventgrid/webhook", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/event-grid-webhook", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/event-grid-webhook.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "blobTriggera6c171691a", "path": "exports/{name}", "type": "blobTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "exportBlobTrigger", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/exportBlobTrigger", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/exportBlobTrigger", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/exportBlobTrigger", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/exportBlobTrigger.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger54367547ae", "route": "management/feature-flags/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "feature-flag-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/feature-flag-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/feature-flag-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/feature-flags/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/feature-flag-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/feature-flag-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger3cecd97ba7", "route": "feature-flags/evaluate", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "feature-flag-evaluate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/feature-flag-evaluate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/feature-flag-evaluate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/feature-flags/evaluate", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/feature-flag-evaluate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/feature-flag-evaluate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger01b7a377f9", "route": "files/process", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "file-process", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/file-process", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/file-process", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/files/process", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/file-process", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/file-process.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerbec34ae3ac", "route": "files/processing/{jobId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "file-processing-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/file-processing-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/file-processing-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/files/processing/{jobid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/file-processing-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/file-processing-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger887ded2c8f", "route": "health", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "health", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/health", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/health", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/health", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/health", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/health.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerc2b074a069", "route": "management/health-checks/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "health-check-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/health-check-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/health-check-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/health-checks/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/health-check-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/health-check-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerf8825c4969", "route": "system/health-status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "health-system", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/health-system", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/health-system", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/system/health-status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/health-system", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/health-system.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"direction": "in", "name": "timerTrigger81a3b71f4c", "schedule": "0 0 * * * *", "type": "timer<PERSON><PERSON>ger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "hourlyHealthCheckTimer", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/hourlyHealthCheckTimer", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/hourlyHealthCheckTimer", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/hourlyHealthCheckTimer", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/hourlyHealthCheckTimer.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger44d9fc0bd8", "route": "integrations", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "integration-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/integration-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/integration-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/integrations", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/integration-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/integration-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger2eecf0105b", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "lemonsqueezy-webhooks", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/lemonsqueezy-webhooks", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/lemonsqueezy-webhooks", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/lemonsqueezy-webhooks", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/lemonsqueezy-webhooks", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/lemonsqueezy-webhooks.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggercf6fe6272d", "route": "logs", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "log-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/log-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/log-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/logs", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/log-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/log-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger29b19bf75e", "route": "logs/query", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "log-query", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/log-query", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/log-query", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/logs/query", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/log-query", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/log-query.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerfe246290a9", "route": "logs/statistics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "log-statistics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/log-statistics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/log-statistics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/logs/statistics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/log-statistics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/log-statistics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger9ff29eb1af", "route": "channels/{channelId}/messages", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "message-list", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/message-list", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/message-list", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/channels/{channelid}/messages", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/message-list", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/message-list.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerfb043ed3ee", "route": "messages", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "message-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/message-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/message-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/messages", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/message-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/message-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger3cf2d79338", "route": "metrics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "metric-record", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/metric-record", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/metric-record", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/metrics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/metric-record", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/metric-record.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger4d4c17fc24", "route": "metrics/collect", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "metrics-collect", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/metrics-collect", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/metrics-collect", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/metrics/collect", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/metrics-collect", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/metrics-collect.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger211537e29d", "route": "metrics/query", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "metrics-query", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/metrics-query", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/metrics-query", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/metrics/query", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/metrics-query", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/metrics-query.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger6a9d2765de", "route": "metrics/summary", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "metrics-summary", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/metrics-summary", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/metrics-summary", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/metrics/summary", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/metrics-summary", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/metrics-summary.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere041099f1e", "route": "management/data-migrations/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "migration-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/migration-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/migration-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/data-migrations/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/migration-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/migration-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerf1494ff3e3", "route": "management/data-migrations/{migrationId}/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "migration-status", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/migration-status", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/migration-status", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/data-migrations/{migrationid}/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/migration-status", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/migration-status.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger989b2ffdba", "route": "mobile/devices/register", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "mobile-device-register", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/mobile-device-register", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/mobile-device-register", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/mobile/devices/register", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/mobile-device-register", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/mobile-device-register.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger60c089d7de", "route": "mobile/offline-data", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "mobile-offline-data", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/mobile-offline-data", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/mobile-offline-data", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/mobile/offline-data", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/mobile-offline-data", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/mobile-offline-data.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger2cd853decf", "route": "mobile/sync", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "mobile-sync", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/mobile-sync", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/mobile-sync", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/mobile/sync", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/mobile-sync", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/mobile-sync.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerded1c4efe5", "route": "management/analytics/models/train", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "model-train", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/model-train", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/model-train", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/analytics/models/train", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/model-train", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/model-train.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggera52f905bd3", "route": "notifications/analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-analytics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-analytics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-analytics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-analytics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-analytics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger72c33c426c", "route": "notifications/{notificationId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/{notificationid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggera91bc96609", "route": "notifications", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-list", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-list", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-list", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-list", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-list.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PATCH", "OPTIONS"], "name": "httpTrigger583ae922f3", "route": "notifications/mark-read", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-mark-read", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-mark-read", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-mark-read", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/mark-read", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-mark-read", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-mark-read.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggera446ca65ac", "route": "notifications/preferences", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-preferences-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-preferences-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-preferences-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/preferences", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-preferences-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-preferences-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere26b24b4b3", "route": "notifications/preferences/reset", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-preferences-reset", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-preferences-reset", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-preferences-reset", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/preferences/reset", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-preferences-reset", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-preferences-reset.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger9cc9ab596f", "route": "notifications/preferences/update", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-preferences-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-preferences-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-preferences-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/preferences/update", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-preferences-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-preferences-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger596c3bf210", "route": "notifications/send", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/send", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8ac4181e95", "route": "notifications/track", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notification-tracking-track", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notification-tracking-track", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notification-tracking-track", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/track", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notification-tracking-track", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notification-tracking-track.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "queueTrigger67c309a6ce", "queueName": "notifications", "type": "queueTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "notificationQueue", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/notificationQueue", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/notificationQueue", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/notificationQueue", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/notificationQueue.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger0d0e64a3f5", "route": "organizations/analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-analytics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-analytics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-analytics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-analytics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-analytics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerd49ef9688b", "route": "organizations/{organizationId}/billing", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-billing-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-billing-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-billing-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/billing", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-billing-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-billing-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger266c81a778", "route": "organizations/{organizationId}/billing/subscription", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-billing-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-billing-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-billing-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/billing/subscription", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-billing-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-billing-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "PATCH", "DELETE", "OPTIONS"], "name": "httpTriggere65157e372", "route": "organizations/{organizationId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-manage", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-manage", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-manage", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-manage", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-manage.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger87149dbb52", "route": "organizations/{organizationId}/members/invite", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-members-invite", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-members-invite", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-members-invite", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/members/invite", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-members-invite", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-members-invite.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger1b13892fb7", "route": "organizations/{organizationId}/settings", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-settings-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-settings-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-settings-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/settings", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-settings-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-settings-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTriggera2287f2b5a", "route": "organizations/settings", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-settings-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-settings-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-settings-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/settings", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-settings-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-settings-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger976eec4c06", "route": "organizations/{organizationId}/teams", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organization-teams-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organization-teams-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organization-teams-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/teams", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organization-teams-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organization-teams-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerfdee67c63e", "route": "organizations", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "organizations", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/organizations", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/organizations", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/organizations", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/organizations.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerd0f3843d8c", "route": "performance/alert-rules", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "performance-alert-rule-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/performance-alert-rule-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/performance-alert-rule-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/performance/alert-rules", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/performance-alert-rule-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/performance-alert-rule-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger71bde190c9", "route": "performance/metrics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "performance-metrics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/performance-metrics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/performance-metrics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/performance/metrics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/performance-metrics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/performance-metrics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggered811ba520", "route": "permissions/batch-check", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "permission-batch-check", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/permission-batch-check", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/permission-batch-check", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/permissions/batch-check", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/permission-batch-check", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/permission-batch-check.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerd9273f3b58", "route": "permissions/check", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "permission-check", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/permission-check", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/permission-check", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/permissions/check", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/permission-check", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/permission-check.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerd3cbabf31f", "route": "permissions/grant", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "permission-grant", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/permission-grant", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/permission-grant", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/permissions/grant", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/permission-grant", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/permission-grant.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerecda19bdbe", "route": "analytics/predictions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "prediction-generate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/prediction-generate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/prediction-generate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/analytics/predictions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/prediction-generate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/prediction-generate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "POST"], "name": "httpTriggerd24cd3694a", "route": "sample", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "Production-sample", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/Production-sample", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/Production-sample", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/sample", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/Production-sample", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/Production-sample.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggera29f518ad9", "route": "projects/analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-analytics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-analytics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-analytics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-analytics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-analytics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "PATCH", "DELETE", "OPTIONS"], "name": "httpTrigger54cd5d326c", "route": "projects/{projectId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-manage", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-manage", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-manage", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/{projectid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-manage", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-manage.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere7b6f0bb0d", "route": "projects/members", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-members-add", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-members-add", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-members-add", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/members", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-members-add", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-members-add.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger150a0cc23c", "route": "projects/{projectId}/members", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-members-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-members-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-members-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/{projectid}/members", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-members-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-members-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTriggerdd68f573d0", "route": "projects/members/update", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-members-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-members-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-members-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/members/update", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-members-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-members-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger4d50d7c63d", "route": "projects/{projectId}/settings", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-settings-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-settings-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-settings-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/{projectid}/settings", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-settings-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-settings-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger7912683d98", "route": "projects/settings", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "project-settings-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/project-settings-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/project-settings-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects/settings", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/project-settings-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/project-settings-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger3bac691bc2", "route": "projects", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "projects", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/projects", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/projects", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/projects", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/projects", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/projects.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7ceed9fc97", "route": "notifications/push/register", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "push-notification-register", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/push-notification-register", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/push-notification-register", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/push/register", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/push-notification-register", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/push-notification-register.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger6bd421bfe7", "route": "notifications/push/send", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "push-notification-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/push-notification-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/push-notification-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/push/send", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/push-notification-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/push-notification-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerf9a708ff01", "route": "notifications/push/stats", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "push-notification-stats", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/push-notification-stats", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/push-notification-stats", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/push/stats", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/push-notification-stats", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/push-notification-stats.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerf99a411621", "route": "notifications/push/unregister", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "push-notification-unregister", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/push-notification-unregister", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/push-notification-unregister", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/push/unregister", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/push-notification-unregister", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/push-notification-unregister.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger1a11964ceb", "route": "notifications/push", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "push-notifications-send", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/push-notifications-send", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/push-notifications-send", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/notifications/push", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/push-notifications-send", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/push-notifications-send.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerea101d0cff", "route": "rate-limit/check", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "rate-limit-check", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/rate-limit-check", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/rate-limit-check", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/rate-limit/check", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/rate-limit-check", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/rate-limit-check.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerc088c825e7", "route": "management/rate-limits/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "rate-limit-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/rate-limit-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/rate-limit-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/management/rate-limits/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/rate-limit-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/rate-limit-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggere47edaa42c", "route": "search", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "search", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/search", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/search", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/search", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/search", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/search.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerfa8638a2b2", "route": "search/advanced", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "search-advanced", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/search-advanced", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/search-advanced", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/search/advanced", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/search-advanced", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/search-advanced.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger19acb064d4", "route": "search/documents", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "search-documents", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/search-documents", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/search-documents", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/search/documents", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/search-documents", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/search-documents.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger655b590407", "route": "search/index", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "search-index-document", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/search-index-document", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/search-index-document", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/search/index", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/search-index-document", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/search-index-document.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggera7be13c4ed", "route": "security/incidents", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "security-incident-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/security-incident-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/security-incident-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/security/incidents", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/security-incident-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/security-incident-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger966e52be46", "route": "security/threats/analyze", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "security-threats-analyze", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/security-threats-analyze", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/security-threats-analyze", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/security/threats/analyze", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/security-threats-analyze", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/security-threats-analyze.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger14ab8a81fd", "route": "signalr/broadcast", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "signalr-broadcast", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/signalr-broadcast", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/signalr-broadcast", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/signalr/broadcast", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/signalr-broadcast", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/signalr-broadcast.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7bb3ad4135", "route": "signalr/groups", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "signalr-groups", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/signalr-groups", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/signalr-groups", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/signalr/groups", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/signalr-groups", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/signalr-groups.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8fc08a5156", "route": "signalr/negotiate", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "signalr-negotiate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/signalr-negotiate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/signalr-negotiate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/signalr/negotiate", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/signalr-negotiate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/signalr-negotiate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger587ce12ccd", "route": "storage/sync/bulk", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "storage-bulk-sync", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/storage-bulk-sync", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/storage-bulk-sync", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/storage/sync/bulk", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/storage-bulk-sync", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/storage-bulk-sync.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger7374ddf43f", "route": "storage/configure", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "storage-configure", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/storage-configure", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/storage-configure", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/storage/configure", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/storage-configure", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/storage-configure.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"direction": "in", "name": "eventGridTrigger69f302e322", "type": "eventGridTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "storage-events-trigger", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/storage-events-trigger", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/storage-events-trigger", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/storage-events-trigger", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/storage-events-trigger.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger36439e10c7", "route": "storage/sync/document", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "storage-sync-document", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/storage-sync-document", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/storage-sync-document", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/storage/sync/document", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/storage-sync-document", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/storage-sync-document.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger0e9d85149e", "route": "subscriptions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "subscription-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/subscription-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/subscription-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/subscriptions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/subscription-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/subscription-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger91d50eaf13", "route": "subscriptions/{organizationId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "subscription-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/subscription-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/subscription-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/subscriptions/{organizationid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/subscription-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/subscription-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "anonymous", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger5774f1ecd9", "route": "system/health", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "system-health", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/system-health", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/system-health", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/system/health", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/system-health", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/system-health.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerc87636232c", "route": "system/metrics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "system-metrics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/system-metrics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/system-metrics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/system/metrics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/system-metrics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/system-metrics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "SERVICE_BUS_CONNECTION_STRING", "direction": "in", "name": "serviceBusTrigger8bf5cf31f5", "subscriptionName": "system-monitor", "topicName": "monitoring-events", "type": "serviceBusTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "systemMonitoring", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/systemMonitoring", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/systemMonitoring", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/systemMonitoring", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/systemMonitoring.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger559e87980d", "route": "templates/{templateId}/apply", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "template-apply", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/template-apply", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/template-apply", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/templates/{templateid}/apply", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/template-apply", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/template-apply.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggere8eed93617", "route": "templates", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "template-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/template-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/template-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/templates", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/template-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/template-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggera3a8568e9a", "route": "templates/generate", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "template-generate", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/template-generate", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/template-generate", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/templates/generate", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/template-generate", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/template-generate.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "AzureWebJobsStorage", "direction": "in", "name": "blobTriggerd80dfccce9", "path": "templates/{name}", "type": "blobTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "templateBlobTrigger", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/templateBlobTrigger", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/templateBlobTrigger", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/templateBlobTrigger", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/templateBlobTrigger.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger1cfa798653", "route": "templates/manage", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "templates", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/templates", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/templates", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/templates/manage", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/templates", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/templates.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger307d320df9", "route": "tenants", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "tenant-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/tenant-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/tenant-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/tenants", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/tenant-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/tenant-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggere69d770677", "route": "tenants/{tenantId}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "tenant-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/tenant-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/tenant-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/tenants/{tenantid}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/tenant-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/tenant-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger49b6a3bfc9", "route": "users/activity/analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-activity-analytics", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-activity-analytics", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-activity-analytics", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/activity/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-activity-analytics", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-activity-analytics.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger571d5df79b", "route": "users/activity/track", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-activity-track", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-activity-track", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-activity-track", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/activity/track", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-activity-track", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-activity-track.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger1dab7b36c1", "route": "users/avatar/upload", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-avatar-upload", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-avatar-upload", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-avatar-upload", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/avatar/upload", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-avatar-upload", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-avatar-upload.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger5012eab749", "route": "users/{userId?}/permissions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-permissions", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-permissions", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-permissions", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/{userid?}/permissions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-permissions", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-permissions.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger854bccc789", "route": "users/personalization", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-personalization-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-personalization-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-personalization-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/personalization", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-personalization-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-personalization-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PATCH", "OPTIONS"], "name": "httpTrigger636195fd3c", "route": "users/personalization/update", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-personalization-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-personalization-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-personalization-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/personalization/update", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-personalization-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-personalization-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger1576b39eb2", "route": "users/preferences", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-preferences-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-preferences-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-preferences-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/preferences", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-preferences-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-preferences-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger8392f95997", "route": "users/preferences/reset", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-preferences-reset", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-preferences-reset", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-preferences-reset", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/preferences/reset", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-preferences-reset", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-preferences-reset.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTriggerf2b3ccf600", "route": "users/preferences/update", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-preferences-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-preferences-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-preferences-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/preferences/update", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-preferences-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-preferences-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "PATCH", "OPTIONS"], "name": "httpTrigger129b56aee7", "route": "users/profile", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-profile", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-profile", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-profile", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/profile", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-profile", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-profile.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerdf06bd1fdf", "route": "users/{userId?}/profile", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-profile-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-profile-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-profile-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/{userid?}/profile", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-profile-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-profile-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PATCH", "OPTIONS"], "name": "httpTrigger746defc17c", "route": "users/profile/preferences", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-profile-preferences-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-profile-preferences-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-profile-preferences-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/profile/preferences", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-profile-preferences-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-profile-preferences-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTriggere8ed76e9bd", "route": "users/profile/update", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-profile-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-profile-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-profile-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/profile/update", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-profile-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-profile-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger5343eb0c3b", "route": "users/{userId?}/tenants", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-tenants-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-tenants-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-tenants-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/{userid?}/tenants", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-tenants-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-tenants-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger0c36b05fcd", "route": "organizations/{organizationId}/invite", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-tenants-invite", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-tenants-invite", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-tenants-invite", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/organizations/{organizationid}/invite", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-tenants-invite", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-tenants-invite.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger2e56dc0eb8", "route": "users/tenants/switch", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "user-tenants-switch", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/user-tenants-switch", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/user-tenants-switch", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/users/tenants/switch", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/user-tenants-switch", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/user-tenants-switch.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger5f15a5de86", "route": "webhooks/deliver", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "webhook-delivery", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/webhook-delivery", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/webhook-delivery", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/webhooks/deliver", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/webhook-delivery", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/webhook-delivery.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger544109b989", "route": "webhooks/test", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "webhook-test", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/webhook-test", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/webhook-test", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/webhooks/test", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/webhook-test", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/webhook-test.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger45e81e4e13", "route": "webhooks", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "webhooks", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/webhooks", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/webhooks", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/webhooks", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/webhooks", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/webhooks.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"direction": "in", "name": "timerTriggera6b6cf1ae4", "schedule": "0 0 3 * * 0", "type": "timer<PERSON><PERSON>ger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "weeklyAnalyticsTimer", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/weeklyAnalyticsTimer", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/weeklyAnalyticsTimer", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/weeklyAnalyticsTimer", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/weeklyAnalyticsTimer.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger14307ab5ba", "route": "workflows/{workflowId}/actions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-action", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-action", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-action", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/{workflowid}/actions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-action", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-action.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerd36054ed13", "route": "workflows/bulk-actions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-bulk-action", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-bulk-action", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-bulk-action", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/bulk-actions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-bulk-action", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-bulk-action.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger1f91aa06a6", "route": "workflows/{id}/steps/{stepId}/complete", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-execution-complete-step", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-execution-complete-step", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-execution-complete-step", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/{id}/steps/{stepid}/complete", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-execution-complete-step", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-execution-complete-step.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger4bb61eff77", "route": "workflows/executions", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-execution-start", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-execution-start", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-execution-start", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/executions", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-execution-start", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-execution-start.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggerf3ca7a8a85", "route": "workflows/{id}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/{id}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTriggercc2cdcb4d7", "route": "workflows/analytics", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-monitoring", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-monitoring", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-monitoring", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/analytics", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-monitoring", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-monitoring.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger05e1433006", "route": "workflows/schedules", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-schedule-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-schedule-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-schedule-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/schedules", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-schedule-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-schedule-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["PUT", "OPTIONS"], "name": "httpTrigger73cc4e1ec9", "route": "workflows/schedules/status", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-schedule-status-update", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-schedule-status-update", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-schedule-status-update", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/schedules/status", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-schedule-status-update", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-schedule-status-update.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTriggerff069cf1b7", "route": "workflows/{id}/start", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-start", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-start", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-start", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows/{id}/start", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-start", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-start.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["POST", "OPTIONS"], "name": "httpTrigger04615e8d09", "route": "workflow-templates/create", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-template-create", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-template-create", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-template-create", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflow-templates/create", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-template-create", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-template-create.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTrigger290baebe3d", "route": "workflow-templates", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-templates", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-templates", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-templates", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflow-templates", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-templates", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-templates.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "OPTIONS"], "name": "httpTrigger5c1c0aadfe", "route": "workflow-templates/{id}", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflow-templates-get", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflow-templates-get", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflow-templates-get", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflow-templates/{id}", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflow-templates-get", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflow-templates-get.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"connection": "SERVICE_BUS_CONNECTION_STRING", "direction": "in", "name": "serviceBusTriggercbfb7b0d31", "queueName": "workflow-orchestration", "type": "serviceBusTrigger"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflowOrchestration", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflowOrchestration", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflowOrchestration", "invokeUrlTemplate": null, "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflowOrchestration", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflowOrchestration.dat", "type": "Microsoft.Web/sites/functions"}, {"config": {"bindings": [{"authLevel": "function", "direction": "in", "methods": ["GET", "POST", "OPTIONS"], "name": "httpTriggerc20e62b59e", "route": "workflows", "type": "httpTrigger"}, {"direction": "out", "name": "$return", "type": "http"}], "entryPoint": "", "functionDirectory": "C:\\home\\site\\wwwroot\\dist", "language": "node", "name": "workflows", "scriptFile": "index.js"}, "configHref": null, "files": null, "functionAppId": null, "href": "https://hepzlogic.azurewebsites.net/admin/functions/workflows", "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Web/sites/hepzlogic/functions/workflows", "invokeUrlTemplate": "https://hepzlogic.azurewebsites.net/workflows", "isDisabled": false, "kind": null, "language": "node", "location": "East US", "name": "hepzlogic/workflows", "resourceGroup": "docucontext", "scriptHref": "https://hepzlogic.azurewebsites.net/admin/vfs/site/wwwroot/index.js", "scriptRootPathHref": null, "secretsFileHref": null, "testData": "", "testDataHref": "https://hepzlogic.azurewebsites.net/admin/vfs/data/Functions/sampledata/workflows.dat", "type": "Microsoft.Web/sites/functions"}]