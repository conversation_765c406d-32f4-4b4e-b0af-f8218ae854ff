#!/usr/bin/env node

const JSONColumnEditor = require('./json-column-editor');

// Demo script to show how the JSON Column Editor works
async function runDemo() {
    console.log('🎬 JSON COLUMN EDITOR - DEMO');
    console.log('═'.repeat(50));
    console.log('This demo shows how the JSON Column Editor analyzes your JSON file.\n');

    const editor = new JSONColumnEditor();
    
    try {
        // Set the source file to our test data
        editor.sourceFile = './test-data.json';
        
        // Load and analyze the JSON
        await editor.loadJSON();
        
        // Display the structure analysis
        editor.displayStructure();
        
        // Show all available columns
        editor.displayColumns();
        
        console.log('\n🎯 DEMO COMPLETE');
        console.log('─'.repeat(50));
        console.log('To actually edit the file, run:');
        console.log('  node json-column-editor.js test-data.json');
        console.log('\nOr for your own file:');
        console.log('  node json-column-editor.js your-file.json');
        
    } catch (error) {
        console.log(`❌ Demo error: ${error.message}`);
    } finally {
        editor.rl.close();
    }
}

// Run the demo
if (require.main === module) {
    runDemo().catch(console.error);
}
