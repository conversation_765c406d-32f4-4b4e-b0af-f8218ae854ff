#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class JSONColumnEditor {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        this.sourceFile = '';
        this.destinationFile = '';
        this.jsonData = null;
        this.columns = new Set();
        this.nestedColumns = new Map();
    }

    // Utility method to ask questions
    async question(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, resolve);
        });
    }

    // Validate file path
    validateFilePath(filePath, shouldExist = true) {
        if (!filePath || filePath.trim() === '') {
            return { valid: false, error: 'File path cannot be empty' };
        }

        const resolvedPath = path.resolve(filePath);
        
        if (shouldExist && !fs.existsSync(resolvedPath)) {
            return { valid: false, error: `File does not exist: ${resolvedPath}` };
        }

        if (shouldExist) {
            try {
                const stats = fs.statSync(resolvedPath);
                if (!stats.isFile()) {
                    return { valid: false, error: `Path is not a file: ${resolvedPath}` };
                }
            } catch (error) {
                return { valid: false, error: `Cannot access file: ${error.message}` };
            }
        }

        return { valid: true, path: resolvedPath };
    }

    // Get source file from user
    async getSourceFile() {
        while (true) {
            console.log('\n📁 SOURCE FILE SELECTION');
            console.log('─'.repeat(50));
            
            const input = await this.question('Enter the path to your JSON file: ');
            
            if (input.toLowerCase() === 'exit') {
                console.log('👋 Goodbye!');
                process.exit(0);
            }

            const validation = this.validateFilePath(input, true);
            
            if (!validation.valid) {
                console.log(`❌ Error: ${validation.error}`);
                console.log('💡 Tip: Type "exit" to quit\n');
                continue;
            }

            // Check if it's a JSON file
            if (!validation.path.toLowerCase().endsWith('.json')) {
                console.log('⚠️  Warning: File doesn\'t have .json extension');
                const confirm = await this.question('Continue anyway? (y/n): ');
                if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                    continue;
                }
            }

            this.sourceFile = validation.path;
            console.log(`✅ Source file selected: ${this.sourceFile}`);
            break;
        }
    }

    // Get destination file from user
    async getDestinationFile() {
        while (true) {
            console.log('\n💾 DESTINATION FILE SELECTION');
            console.log('─'.repeat(50));
            
            const defaultDest = this.sourceFile.replace('.json', '_edited.json');
            const input = await this.question(`Enter destination file path (default: ${defaultDest}): `);
            
            const destPath = input.trim() === '' ? defaultDest : input;
            
            if (destPath.toLowerCase() === 'exit') {
                console.log('👋 Goodbye!');
                process.exit(0);
            }

            // Check if destination file already exists
            if (fs.existsSync(destPath)) {
                console.log(`⚠️  File already exists: ${destPath}`);
                const overwrite = await this.question('Overwrite existing file? (y/n): ');
                if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
                    continue;
                }
            }

            // Validate destination directory exists
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
                console.log(`❌ Error: Directory does not exist: ${destDir}`);
                continue;
            }

            this.destinationFile = path.resolve(destPath);
            console.log(`✅ Destination file set: ${this.destinationFile}`);
            break;
        }
    }

    // Clean file content from BOM and other encoding issues
    cleanFileContent(content) {
        // Remove BOM (Byte Order Mark) if present
        if (content.charCodeAt(0) === 0xFEFF) {
            content = content.slice(1);
            console.log('🔧 Removed BOM (Byte Order Mark)');
        }

        // Remove other common problematic characters at the beginning
        content = content.replace(/^[\uFEFF\u200B-\u200D\u2060\uFFFE\uFFFF]/g, '');

        // Trim whitespace
        content = content.trim();

        return content;
    }

    // Load and parse JSON file
    async loadJSON() {
        try {
            console.log('\n📖 LOADING JSON FILE');
            console.log('─'.repeat(50));

            let fileContent = fs.readFileSync(this.sourceFile, 'utf8');
            console.log(`📊 Original file size: ${(fileContent.length / 1024).toFixed(2)} KB`);

            // Clean the content
            const originalLength = fileContent.length;
            fileContent = this.cleanFileContent(fileContent);

            if (fileContent.length !== originalLength) {
                console.log(`🔧 Cleaned ${originalLength - fileContent.length} problematic characters`);
            }

            // Try to parse JSON
            this.jsonData = JSON.parse(fileContent);
            console.log('✅ JSON file loaded and parsed successfully');

            // Analyze the structure
            this.analyzeStructure();

        } catch (error) {
            console.log(`❌ Error loading JSON file: ${error.message}`);

            if (error instanceof SyntaxError) {
                console.log('💡 The file contains invalid JSON syntax');
                console.log('\n🔍 TROUBLESHOOTING SUGGESTIONS:');
                console.log('1. Check if the file is actually a JSON file');
                console.log('2. Verify the file encoding (should be UTF-8)');
                console.log('3. Look for missing commas, brackets, or quotes');
                console.log('4. Try opening the file in a text editor to inspect it');

                // Show first few characters for debugging
                try {
                    const rawContent = fs.readFileSync(this.sourceFile, 'utf8');
                    const preview = rawContent.substring(0, 100).replace(/\r?\n/g, '\\n');
                    console.log(`\n📝 First 100 characters: "${preview}"`);

                    // Show character codes for the first few characters
                    const charCodes = rawContent.substring(0, 10).split('').map(char =>
                        `${char} (${char.charCodeAt(0)})`
                    ).join(', ');
                    console.log(`🔢 Character codes: ${charCodes}`);
                } catch (debugError) {
                    console.log('Could not read file for debugging');
                }
            }

            // Ask user if they want to try alternative approaches
            const tryAlternative = await this.question('\n🤔 Would you like to try alternative loading methods? (y/n): ');
            if (tryAlternative.toLowerCase() === 'y' || tryAlternative.toLowerCase() === 'yes') {
                await this.tryAlternativeLoading();
            } else {
                process.exit(1);
            }
        }
    }

    // Try alternative methods to load the JSON file
    async tryAlternativeLoading() {
        console.log('\n🔄 TRYING ALTERNATIVE LOADING METHODS');
        console.log('─'.repeat(50));

        const encodings = ['utf8', 'utf16le', 'latin1', 'ascii'];

        for (const encoding of encodings) {
            try {
                console.log(`🔍 Trying encoding: ${encoding}`);
                let content = fs.readFileSync(this.sourceFile, encoding);
                content = this.cleanFileContent(content);

                this.jsonData = JSON.parse(content);
                console.log(`✅ Success with ${encoding} encoding!`);

                // Analyze the structure
                this.analyzeStructure();
                return;

            } catch (error) {
                console.log(`❌ Failed with ${encoding}: ${error.message.substring(0, 50)}...`);
            }
        }

        console.log('\n💥 All loading methods failed. Please check your JSON file.');
        process.exit(1);
    }

    // Recursively analyze JSON structure to find all columns
    analyzeStructure(obj = this.jsonData, prefix = '', level = 0) {
        if (level > 10) return; // Prevent infinite recursion
        
        if (Array.isArray(obj)) {
            if (obj.length > 0) {
                this.analyzeStructure(obj[0], prefix, level + 1);
            }
        } else if (obj && typeof obj === 'object') {
            for (const key in obj) {
                const fullKey = prefix ? `${prefix}.${key}` : key;
                this.columns.add(fullKey);
                
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    this.nestedColumns.set(fullKey, typeof obj[key]);
                    this.analyzeStructure(obj[key], fullKey, level + 1);
                }
            }
        }
    }

    // Display structure information
    displayStructure() {
        console.log('\n🔍 JSON STRUCTURE ANALYSIS');
        console.log('─'.repeat(50));
        
        if (Array.isArray(this.jsonData)) {
            console.log(`📋 Type: Array with ${this.jsonData.length} items`);
        } else if (typeof this.jsonData === 'object') {
            console.log('📋 Type: Object');
        }
        
        console.log(`🔑 Total unique columns found: ${this.columns.size}`);
        
        if (this.nestedColumns.size > 0) {
            console.log(`🌳 Nested objects found: ${this.nestedColumns.size}`);
        }
    }

    // Display all columns with numbers for selection
    displayColumns() {
        console.log('\n📋 AVAILABLE COLUMNS');
        console.log('─'.repeat(50));
        
        const columnArray = Array.from(this.columns).sort();
        
        columnArray.forEach((column, index) => {
            const isNested = this.nestedColumns.has(column);
            const nestedIndicator = isNested ? ' 🌳' : '';
            console.log(`${(index + 1).toString().padStart(3)}: ${column}${nestedIndicator}`);
        });
        
        console.log('\n🌳 = Nested object/array');
        console.log(`\nTotal: ${columnArray.length} columns`);
    }

    // Get user selection for columns to delete
    async getColumnsToDelete() {
        const columnArray = Array.from(this.columns).sort();
        const columnsToDelete = [];

        console.log('\n🗑️  COLUMN DELETION SELECTION');
        console.log('─'.repeat(50));
        console.log('Select columns to DELETE (you can specify multiple ways):');
        console.log('• Single number: 5');
        console.log('• Multiple numbers: 1,3,5,7');
        console.log('• Range: 1-5');
        console.log('• Mixed: 1,3,7-10,15');
        console.log('• Type "none" to skip deletion');
        console.log('• Type "all" to delete all columns');
        console.log('• Type "show" to display columns again');

        while (true) {
            const input = await this.question('\nEnter your selection: ');

            if (input.toLowerCase() === 'exit') {
                console.log('👋 Goodbye!');
                process.exit(0);
            }

            if (input.toLowerCase() === 'none') {
                console.log('✅ No columns will be deleted');
                break;
            }

            if (input.toLowerCase() === 'all') {
                const confirm = await this.question('⚠️  Are you sure you want to delete ALL columns? (yes/no): ');
                if (confirm.toLowerCase() === 'yes') {
                    columnsToDelete.push(...columnArray);
                    console.log(`✅ All ${columnArray.length} columns selected for deletion`);
                    break;
                }
                continue;
            }

            if (input.toLowerCase() === 'show') {
                this.displayColumns();
                continue;
            }

            try {
                const selectedIndices = this.parseSelection(input, columnArray.length);
                const selectedColumns = selectedIndices.map(i => columnArray[i - 1]);

                console.log('\n📋 Selected columns for deletion:');
                selectedColumns.forEach((col, idx) => {
                    console.log(`  ${idx + 1}. ${col}`);
                });

                const confirm = await this.question(`\nConfirm deletion of ${selectedColumns.length} columns? (y/n): `);
                if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
                    columnsToDelete.push(...selectedColumns);
                    console.log(`✅ ${selectedColumns.length} columns selected for deletion`);
                    break;
                }

            } catch (error) {
                console.log(`❌ Error: ${error.message}`);
                console.log('💡 Please try again with valid selection format');
            }
        }

        return columnsToDelete;
    }

    // Parse user selection (handles ranges, comma-separated values, etc.)
    parseSelection(input, maxNumber) {
        const indices = new Set();
        const parts = input.split(',').map(part => part.trim());

        for (const part of parts) {
            if (part.includes('-')) {
                // Handle range (e.g., "1-5")
                const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                if (isNaN(start) || isNaN(end) || start < 1 || end > maxNumber || start > end) {
                    throw new Error(`Invalid range: ${part}. Must be between 1 and ${maxNumber}`);
                }
                for (let i = start; i <= end; i++) {
                    indices.add(i);
                }
            } else {
                // Handle single number
                const num = parseInt(part);
                if (isNaN(num) || num < 1 || num > maxNumber) {
                    throw new Error(`Invalid number: ${part}. Must be between 1 and ${maxNumber}`);
                }
                indices.add(num);
            }
        }

        return Array.from(indices).sort((a, b) => a - b);
    }

    // Remove specified columns from the JSON data
    removeColumns(columnsToDelete) {
        if (columnsToDelete.length === 0) {
            console.log('ℹ️  No columns to delete');
            return;
        }

        console.log('\n🔄 PROCESSING DELETIONS');
        console.log('─'.repeat(50));

        let deletedCount = 0;

        // Sort columns by depth (deepest first) to avoid issues with nested deletions
        const sortedColumns = columnsToDelete.sort((a, b) => {
            const depthA = (a.match(/\./g) || []).length;
            const depthB = (b.match(/\./g) || []).length;
            return depthB - depthA;
        });

        for (const column of sortedColumns) {
            try {
                if (this.deleteProperty(this.jsonData, column)) {
                    deletedCount++;
                    console.log(`✅ Deleted: ${column}`);
                } else {
                    console.log(`⚠️  Not found: ${column}`);
                }
            } catch (error) {
                console.log(`❌ Error deleting ${column}: ${error.message}`);
            }
        }

        console.log(`\n📊 Summary: ${deletedCount}/${columnsToDelete.length} columns deleted successfully`);
    }

    // Delete a property from nested object using dot notation
    deleteProperty(obj, propertyPath) {
        if (Array.isArray(obj)) {
            // If it's an array, apply deletion to all items
            let deleted = false;
            for (const item of obj) {
                if (this.deleteProperty(item, propertyPath)) {
                    deleted = true;
                }
            }
            return deleted;
        }

        const parts = propertyPath.split('.');
        const lastPart = parts.pop();
        let current = obj;

        // Navigate to the parent object
        for (const part of parts) {
            if (current && typeof current === 'object' && part in current) {
                current = current[part];
                if (Array.isArray(current) && current.length > 0) {
                    // If we encounter an array, we need to delete from all items
                    let deleted = false;
                    for (const item of current) {
                        if (this.deleteProperty(item, parts.slice(parts.indexOf(part) + 1).concat(lastPart).join('.'))) {
                            deleted = true;
                        }
                    }
                    return deleted;
                }
            } else {
                return false; // Path doesn't exist
            }
        }

        // Delete the property if it exists
        if (current && typeof current === 'object' && lastPart in current) {
            delete current[lastPart];
            return true;
        }

        return false;
    }

    // Save the modified JSON to destination file
    async saveJSON() {
        try {
            console.log('\n💾 SAVING MODIFIED JSON');
            console.log('─'.repeat(50));

            const jsonString = JSON.stringify(this.jsonData, null, 2);
            fs.writeFileSync(this.destinationFile, jsonString, 'utf8');

            const stats = fs.statSync(this.destinationFile);
            console.log(`✅ File saved successfully!`);
            console.log(`📁 Location: ${this.destinationFile}`);
            console.log(`📊 Size: ${(stats.size / 1024).toFixed(2)} KB`);

            // Show size comparison
            const originalStats = fs.statSync(this.sourceFile);
            const sizeDiff = stats.size - originalStats.size;
            const sizeChangePercent = ((sizeDiff / originalStats.size) * 100).toFixed(1);

            if (sizeDiff < 0) {
                console.log(`📉 Size reduced by ${Math.abs(sizeDiff)} bytes (${Math.abs(sizeChangePercent)}%)`);
            } else if (sizeDiff > 0) {
                console.log(`📈 Size increased by ${sizeDiff} bytes (${sizeChangePercent}%)`);
            } else {
                console.log(`📊 Size unchanged`);
            }

        } catch (error) {
            console.log(`❌ Error saving file: ${error.message}`);
            throw error;
        }
    }

    // Display final summary
    displaySummary() {
        console.log('\n🎉 OPERATION COMPLETED');
        console.log('─'.repeat(50));
        console.log(`📥 Source: ${this.sourceFile}`);
        console.log(`📤 Destination: ${this.destinationFile}`);
        console.log('✅ JSON column editing completed successfully!');
        console.log('\n💡 Tips for next time:');
        console.log('• You can run this script again on the output file');
        console.log('• Use version control to track your changes');
        console.log('• Always backup important files before editing');
    }

    // Parse command line arguments
    parseCommandLineArgs() {
        const args = process.argv.slice(2);
        const options = {
            sourceFile: null,
            destinationFile: null,
            help: false
        };

        for (let i = 0; i < args.length; i++) {
            const arg = args[i];

            if (arg === '--help' || arg === '-h') {
                options.help = true;
            } else if (arg === '--source' || arg === '-s') {
                options.sourceFile = args[++i];
            } else if (arg === '--dest' || arg === '-d') {
                options.destinationFile = args[++i];
            } else if (!options.sourceFile && !arg.startsWith('-')) {
                // First non-flag argument is source file
                options.sourceFile = arg;
            } else if (!options.destinationFile && !arg.startsWith('-')) {
                // Second non-flag argument is destination file
                options.destinationFile = arg;
            }
        }

        return options;
    }

    // Display help information
    displayHelp() {
        console.log('🚀 JSON COLUMN EDITOR - Help');
        console.log('═'.repeat(50));
        console.log('A tool to remove columns from JSON files interactively.\n');

        console.log('USAGE:');
        console.log('  node json-column-editor.js [options] [source-file] [dest-file]\n');

        console.log('OPTIONS:');
        console.log('  -h, --help              Show this help message');
        console.log('  -s, --source <file>     Source JSON file path');
        console.log('  -d, --dest <file>       Destination file path\n');

        console.log('EXAMPLES:');
        console.log('  node json-column-editor.js');
        console.log('  node json-column-editor.js data.json');
        console.log('  node json-column-editor.js data.json output.json');
        console.log('  node json-column-editor.js --source data.json --dest output.json\n');

        console.log('FEATURES:');
        console.log('• Works with any JSON structure (objects, arrays, nested data)');
        console.log('• Interactive column selection with multiple input methods');
        console.log('• Handles encoding issues (BOM, UTF-8, etc.)');
        console.log('• Safe operations with confirmation prompts');
        console.log('• Detailed progress logging and error handling\n');

        console.log('For more information, visit: https://github.com/your-repo');
    }

    // Main execution flow
    async run() {
        try {
            // Parse command line arguments
            const options = this.parseCommandLineArgs();

            if (options.help) {
                this.displayHelp();
                return;
            }

            console.log('🚀 JSON COLUMN EDITOR');
            console.log('═'.repeat(50));
            console.log('Welcome! This tool helps you remove columns from JSON files.');
            console.log('You can work with any JSON structure - objects, arrays, nested data.');
            console.log('Type "exit" at any prompt to quit.\n');

            // Step 1: Get source file (use command line arg if provided)
            if (options.sourceFile) {
                const validation = this.validateFilePath(options.sourceFile, true);
                if (validation.valid) {
                    this.sourceFile = validation.path;
                    console.log(`📁 Using source file from command line: ${this.sourceFile}`);
                } else {
                    console.log(`❌ Invalid source file from command line: ${validation.error}`);
                    await this.getSourceFile();
                }
            } else {
                await this.getSourceFile();
            }

            // Step 2: Get destination file (use command line arg if provided)
            if (options.destinationFile) {
                this.destinationFile = path.resolve(options.destinationFile);
                console.log(`💾 Using destination file from command line: ${this.destinationFile}`);

                // Check if destination file already exists
                if (fs.existsSync(this.destinationFile)) {
                    console.log(`⚠️  File already exists: ${this.destinationFile}`);
                    const overwrite = await this.question('Overwrite existing file? (y/n): ');
                    if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
                        await this.getDestinationFile();
                    }
                }
            } else {
                await this.getDestinationFile();
            }

            // Step 3: Load and analyze JSON
            await this.loadJSON();

            // Step 4: Display structure
            this.displayStructure();

            // Step 5: Show all columns
            this.displayColumns();

            // Step 6: Get columns to delete
            const columnsToDelete = await this.getColumnsToDelete();

            // Step 7: Remove selected columns
            this.removeColumns(columnsToDelete);

            // Step 8: Save modified JSON
            await this.saveJSON();

            // Step 9: Display summary
            this.displaySummary();

        } catch (error) {
            console.log(`\n💥 Fatal error: ${error.message}`);
            console.log('Please check your input and try again.');
        } finally {
            this.rl.close();
        }
    }
}

// Run the application
if (require.main === module) {
    const editor = new JSONColumnEditor();
    editor.run().catch(error => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = JSONColumnEditor;
